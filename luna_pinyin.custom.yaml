# luna_pinyin.custom.yaml
#
# 補靪功能：將朙月拼音的詞庫修改爲朙月拼音擴充詞庫
#
# <AUTHOR> <EMAIL>
#
# 部署位置：
# ~/.config/ibus/rime  (Linux)
# ~/Library/Rime  (Mac OS)
# %APPDATA%\Rime  (Windows)
#
# 於重新部署後生效
#
#
# 注意：本補靪適用於所有朙月拼音系列方案（「朙月拼音」、「朙月拼音·简化字」、「朙月拼音·臺灣正體」、「朙月拼音·語句流」）。
# 只須將本 custom.yaml 的前面名字改爲對應的輸入方案名字然後放入用戶文件夾重新部署即可。如 luna_pinyin_simp.custom.yaml。
# 雙拼用戶請使用 double_pinyin.custom.yaml。
#
#
# 附朙月拼音系列方案與其對應的 id 一覽表：
# 輸入方案  id
# 朙月拼音  luna_pinyin
# 朙月拼音·简化字  luna_pinyin_simp
# 朙月拼音·臺灣正體 luna_pinyin_tw
# 朙月拼音·語句流  luna_pinyin_fluency


#### 需要反查，打开所有带“反查”注释的行，和 reverse_lookup 块  #####
patch:
  schema:
    name: 明月拼音
    # dependencies: # 反查
    #   - liangfen # 反查
  
  # 載入朙月拼音擴充詞庫
  "translator/dictionary": luna_pinyin.extended
  #  拼写纠错
  "translator/enable_correction": false
  # 本條補靪只在「小狼毫 0.9.30」、「鼠鬚管 0.9.25 」、「Rime-1.2」及更高的版本中起作用。
  "speller/algebra/@before 0": xform/^([b-df-np-z])$/$1_/
  #"switches/@0/reset": 1  #表示將 switcher 列表中的第一個元素（即 ascii_mode 開關）的初始值重設爲狀態1（即「英文」）。
#模糊拼音
  'speller/algebra':
    - erase/^xx$/                      # 第一行保留
    # 模糊音定義
    # 需要哪組就刪去行首的 # 號，單雙向任選
    #- derive/^([zcs])h/$1/             # zh, ch, sh => z, c, s
    #- derive/^([zcs])([^h])/$1h$2/     # z, c, s => zh, ch, sh

    #- derive/^n/l/                     # n => l
    #- derive/^l/n/                     # l => n

    # 這兩組一般是單向的
    #- derive/^r/l/                     # r => l

    #- derive/^ren/yin/                 # ren => yin, reng => ying
    #- derive/^shen/sehn/
    #- derive/^r/y/                     # r => y

    # 下面 hu <=> f 這組寫法複雜一些，分情況討論
    #- derive/^hu$/fu/                  # hu => fu
    #- derive/^hong$/feng/              # hong => feng
    #- derive/^hu([in])$/fe$1/          # hui => fei, hun => fen
    #- derive/^hu([ao])/f$1/            # hua => fa, ...

    #- derive/^fu$/hu/                  # fu => hu
    #- derive/^feng$/hong/              # feng => hong
    #- derive/^fe([in])$/hu$1/          # fei => hui, fen => hun
    #- derive/^f([ao])/hu$1/            # fa => hua, ...

    # 韻母部份
    #- derive/^([bpmf])eng$/$1ong/      # meng = mong, ...
    #- derive/([ei])n$/$1ng/            # en => eng, in => ing
    #- derive/([ei])ng$/$1n/            # eng => en, ing => in
 
    #自制
    #- derive/^([sj])hen$/$1ehn/

    # 樣例足夠了，其他請自己總結……

    # 反模糊音？
    # 誰說方言沒有普通話精確、有模糊音，就能有反模糊音。
    # 示例爲分尖團的中原官話：
    #- derive/^ji$/zii/   # 在設計者安排下鳩佔鵲巢，尖音i只好雙寫了
    #- derive/^qi$/cii/
    #- derive/^xi$/sii/
    #- derive/^ji/zi/
    #- derive/^qi/ci/
    #- derive/^xi/si/
    #- derive/^ju/zv/
    #- derive/^qu/cv/
    #- derive/^xu/sv/
    # 韻母部份，只能從大面上覆蓋
    #- derive/^([bpm])o$/$1eh/          # bo => beh, ...
    #- derive/(^|[dtnlgkhzcs]h?)e$/$1eh/  # ge => geh, se => sheh, ...
    #- derive/^([gkh])uo$/$1ue/         # guo => gue, ...
    #- derive/^([gkh])e$/$1uo/          # he => huo, ...
    #- derive/([uv])e$/$1o/             # jue => juo, lve => lvo, ...
    #- derive/^fei$/fi/                 # fei => fi
    #- derive/^wei$/vi/                 # wei => vi
    #- derive/^([nl])ei$/$1ui/          # nei => nui, lei => lui
    #- derive/^([nlzcs])un$/$1vn/       # lun => lvn, zun => zvn, ... 
    #- derive/^([nlzcs])ong$/$1iong/    # long => liong, song => siong, ...
    # 這個辦法雖從拼寫上做出了區分，然而受詞典制約，候選字仍是混的。
    # 只有真正的方音輸入方案纔能做到！但「反模糊音」這個玩法快速而有效！

    # 模糊音定義先於簡拼定義，方可令簡拼支持以上模糊音
    - abbrev/^([a-z]).+$/$1/           # 簡拼（首字母）
    - abbrev/^([zcs]h).+$/$1/          # 簡拼（zh, ch, sh）

    # 以下是一組容錯拼寫，《漢語拼音》方案以前者爲正
    - derive/^([nl])ve$/$1ue/          # nve = nue, lve = lue
    - derive/^([jqxy])u/$1v/           # ju = jv,
    - derive/un$/uen/                  # gun = guen,
    - derive/ui$/uei/                  # gui = guei,
    - derive/iu$/iou/                  # jiu = jiou,

    # 自動糾正一些常見的按鍵錯誤
    - derive/([aeiou])ng$/$1gn/        # dagn => dang
    - derive/([dtngkhrzcs])o(u|ng)$/$1o/  # zho => zhong|zhou
    - derive/ong$/on/                  # zhonguo => zhong guo
    - derive/ao$/oa/                   # hoa => hao
    - derive/([iu])a(o|ng?)$/a$1$2/    # tain => tian

  # 分尖團後 v => ü 的改寫條件也要相應地擴充：
  #'translator/preedit_format':
   #- "xform/([nljqxyzcs])v/$1ü/"

  switches:
    - name: ascii_mode
      reset: 0
      states: [ 中文, 西文 ]
    - name: emoji_suggestion
      reset: 1
      states: [ "Yes", "No" ]
    - name: full_shape
      states: [ 半角, 全角 ]
    - name: simplification
      reset: 1
      states: [ 漢字, 汉字 ]
    - name: ascii_punct
      states: [ 。，, ．， ]
  engine/translators:
    # - lua_translator@date_translator
    # - lua_translator@week_translator
    - punct_translator
    - script_translator
    - table_translator@custom_phrase
    # - reverse_lookup_translator #反查
  engine/filters:
    - simplifier@emoji_suggestion
    #- simplifier@zh_simp
    - simplifier
    - uniquifier
    #- charset_filter@gbk
    #- single_char_filter
  engine/processors:
    - ascii_composer
    - recognizer
    - key_binder
    - speller
    - punctuator
    - selector
    - navigator
    - express_editor
  # engine/segmentors:
  #   - ascii_segmentor
  #   - matcher
  #   - abc_segmentor
  #   - punct_segmentor
  #   - fallback_segmentor
  emoji_suggestion:
    opencc_config: emoji.json
    option_name: emoji_suggestion
    # tips: all
  
  punctuator:
    import_preset: symbols
    # 自定义快捷符号输入
    # symbols:
    #   "/fs": [½, ‰, ¼, ⅓, ⅔, ¾, ⅒ ]
    half_shape:
      "#": "#"
      "*": "*"
      "`": "`"
      "~": "~"
      "@": "@"
      "=": "="
      "/": ["/", "÷",]
      '\': "、"
      "_" : "──"
      "'": {pair: ["「", "」"]}
      "[": ["【", "["]
      "]": ["】", "]"]
      "$": ["¥", "$", "€", "£", "¢", "¤"]
      "<": ["《", "〈", "«", "<"]
      ">": ["》", "〉", "»", ">"]

  # 反查配置，若需要反查，则反注释 reverse_lookup 块
  # reverse_lookup:
  #   # 反查詞典設定
  #   dictionary: liangfen
  #   # 提前顯示尚未輸入完整碼的字〔僅 table_translator 有效〕
  #   enable_completion: true
  #   # 前綴
  #   prefix: "`"
  #   # 後綴
  #   suffix: "'"
  #   # 開始提示符
  #   tips: 〔两分〕
  #   # 上屏碼格
  #   preedit_format:
  #     - xlit/hspnz/一丨丿丶乙/
  #   # 提示碼格式
  #   comment_format:
  #     - xform/([nl])v/$1ü/

  recognizer:
    patterns:
      punct: "^/([0-9]0?|[A-Za-z]+)$"
      # reverse_lookup: "`[a-z]*'?$" # 反查


  # 自定义词库
  custom_phrase:
    dictionary: ""
    user_dict: custom_phrase
    db_class: stabledb
    enable_completion: false
    enable_sentence: false
    initial_quality: 1
  "engine/translators/@5": table_translator@custom_phrase

# Rx: BlindingDark/rime-easy-en:customize:schema=double_pinyin_flypy
  # __include: easy_en:/patch
  # easy_en/enable_sentence: false
# Rx: lotem/rime-octagram-data:customize:schema=luna_pinyin,model=hans
  __include: grammar:/hant
# Rx: BlindingDark/rime-lua-select-character:customize:schema=luna_pinyin
  # __include: lua_select_character:/patch #需要以词定字插件打开本行注释