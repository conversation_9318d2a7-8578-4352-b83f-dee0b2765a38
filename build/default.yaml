__build_info:
  rime_version: 1.13.0
  timestamps:
    default: 1737643099
    default.custom: 1711681265
    key_bindings: 1737643099
    key_bindings.custom: 0
    punctuation: 1737643099
    punctuation.custom: 0
ascii_composer:
  good_old_caps_lock: true
  switch_key:
    Caps_Lock: commit_code
    Control_L: noop
    Control_R: noop
    Eisu_toggle: clear
    Shift_L: commit_code
    Shift_R: noop
config_version: 0.40
key_binder:
  bindings:
    - {accept: Tab, send: Right, when: composing}
    - {accept: "Shift+Tab", send: Page_Up, when: composing}
    - {accept: minus, send: Page_Up, when: paging}
    - {accept: equal, send: Page_Down, when: has_menu}
    - {accept: bracketleft, send: Page_Up, when: paging}
    - {accept: bracketright, send: Page_Down, when: has_menu}
menu:
  page_size: 8
punctuator:
  full_shape:
    " ": {commit: "　"}
    "!": {commit: "！"}
    "\"": {pair: ["“", "”"]}
    "#": ["＃", "⌘"]
    "$": ["￥", "$", "€", "£", "¥", "¢", "¤"]
    "%": ["％", "°", "℃"]
    "&": "＆"
    "'": {pair: ["‘", "’"]}
    "(": "（"
    ")": "）"
    "*": ["＊", "·", "・", "×", "※", "❂"]
    "+": "＋"
    ",": {commit: "，"}
    "-": "－"
    .: {commit: "。"}
    "/": ["／", "÷"]
    ":": {commit: "："}
    ";": {commit: "；"}
    "<": ["《", "〈", "«", "‹"]
    "=": "＝"
    ">": ["》", "〉", "»", "›"]
    "?": {commit: "？"}
    "@": ["＠", "☯"]
    "[": ["「", "【", "〔", "［"]
    "\\": ["、", "＼"]
    "]": ["」", "】", "〕", "］"]
    "^": {commit: "……"}
    _: "——"
    "`": "｀"
    "{": ["『", "〖", "｛"]
    "|": ["·", "｜", "§", "¦"]
    "}": ["』", "〗", "｝"]
    "~": "～"
  half_shape:
    "!": {commit: "！"}
    "\"": {pair: ["“", "”"]}
    "#": "#"
    "$": ["￥", "$", "€", "£", "¥", "¢", "¤"]
    "%": ["%", "％", "°", "℃"]
    "&": "&"
    "'": {pair: ["‘", "’"]}
    "(": "（"
    ")": "）"
    "*": ["*", "＊", "·", "・", "×", "※", "❂"]
    "+": "+"
    ",": {commit: "，"}
    "-": "-"
    .: {commit: "。"}
    "/": ["、", "/", "／", "÷"]
    ":": {commit: "："}
    ";": {commit: "；"}
    "<": ["《", "〈", "«", "‹"]
    "=": "="
    ">": ["》", "〉", "»", "›"]
    "?": {commit: "？"}
    "@": "@"
    "[": ["「", "【", "〔", "［"]
    "\\": ["、", "\\", "＼"]
    "]": ["」", "】", "〕", "］"]
    "^": {commit: "……"}
    _: "——"
    "`": "`"
    "{": ["『", "〖", "｛"]
    "|": ["·", "|", "｜", "§", "¦"]
    "}": ["』", "〗", "｝"]
    "~": ["~", "～"]
recognizer:
  patterns:
    email: "^[A-Za-z][-_.0-9A-Za-z]*@.*$"
    uppercase: "[A-Z][-_+.'0-9A-Za-z]*$"
    url: "^(www[.]|https?:|ftp[.:]|mailto:|file:).*$|^[a-z]+[.].+$"
schema_list:
  - schema: double_pinyin_flypy
  - schema: luna_pinyin
switcher:
  abbreviate_options: true
  caption: "〔方案選單〕"
  fold_options: true
  hotkeys:
    - "Control+grave"
  option_list_separator: "／"
  save_options:
    - full_shape
    - ascii_punct
    - simplification
    - zh_hans
    - emoji_suggestion