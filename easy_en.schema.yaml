﻿# Rime schema
# vim: set sw=2 sts=2 et:
# encoding: utf-8

schema:
  schema_id: easy_en
  name: Easy English
  version: "0.5"
  author:
    - <PERSON> <<EMAIL>>
    - BlindingDark <<EMAIL>>
  description:
    Easy English

switches:
  - name: ascii_mode
    reset: 0
    states: [ ASCII-OFF, ASCII-ON ]

engine:
  processors:
    - ascii_composer
    - key_binder
    - speller
    - recognizer
    - punctuator
    - selector
    - navigator
    - express_editor
  segmentors:
    - matcher
    - ascii_segmentor
    - abc_segmentor
    - punct_segmentor
    - fallback_segmentor
  translators:
    - table_translator
    - punct_translator
  filters:
    - uniquifier
    - lua_filter@append_blank_filter

speller:
  alphabet: zyxwvutsrqponmlkjihgfedcbaZYXWVUTSRQPONMLKJIHGFEDCBA-_
  delimiter: " '"

translator:
  dictionary: easy_en
  spelling_hints: 9
  comment_format:
    - xform/^.+$//

key_binder:
  import_preset: default

punctuator:
  import_preset: default

recognizer:
  import_preset: default
  patterns:
    uppercase: ''
