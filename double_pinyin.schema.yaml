# Rime schema
# encoding: utf-8

schema:
  schema_id: double_pinyin
  name: 自然碼雙拼
  version: "0.15"
  author:
    - 佛振 <<EMAIL>>
  description: |
    朙月拼音＋自然碼雙拼方案。
  dependencies:
    - stroke

switches:
  - name: ascii_mode
    reset: 0
    states: [ 中文, 西文 ]
  - name: full_shape
    states: [ 半角, 全角 ]
  - name: simplification
    states: [ 漢字, 汉字 ]
  - name: ascii_punct
    states: [ 。，, ．， ]

engine:
  processors:
    - ascii_composer
    - recognizer
    - key_binder
    - speller
    - punctuator
    - selector
    - navigator
    - express_editor
  segmentors:
    - ascii_segmentor
    - matcher
    - abc_segmentor
    - punct_segmentor
    - fallback_segmentor
  translators:
    - punct_translator
    - reverse_lookup_translator
    - script_translator
  filters:
    - simplifier
    - uniquifier

speller:
  alphabet: zyxwvutsrqponmlkjihgfedcba
  delimiter: " '"
  algebra:
    - erase/^xx$/
    - derive/^([jqxy])u$/$1v/
    - derive/^([aoe])([ioun])$/$1$1$2/
    - xform/^([aoe])(ng)?$/$1$1$2/
    - xform/iu$/Q/
    - xform/[iu]a$/W/
    - xform/[uv]an$/R/
    - xform/[uv]e$/T/
    - xform/ing$|uai$/Y/
    - xform/^sh/U/
    - xform/^ch/I/
    - xform/^zh/V/
    - xform/uo$/O/
    - xform/[uv]n$/P/
    - xform/i?ong$/S/
    - xform/[iu]ang$/D/
    - xform/(.)en$/$1F/
    - xform/(.)eng$/$1G/
    - xform/(.)ang$/$1H/
    - xform/ian$/M/
    - xform/(.)an$/$1J/
    - xform/iao$/C/
    - xform/(.)ao$/$1K/
    - xform/(.)ai$/$1L/
    - xform/(.)ei$/$1Z/
    - xform/ie$/X/
    - xform/ui$/V/
    - xform/(.)ou$/$1B/
    - xform/in$/N/
    - xlit/QWRTYUIOPSDFGHMJCKLZXVBN/qwrtyuiopsdfghmjcklzxvbn/
    #- abbrev/^(.).+$/$1/

translator:
  dictionary: luna_pinyin
  prism: double_pinyin
  preedit_format:
    - xform/([bpmnljqxy])n/$1in/
    - xform/(\w)g/$1eng/
    - xform/(\w)q/$1iu/
    - xform/([gkhvuirzcs])w/$1ua/
    - xform/(\w)w/$1ia/
    - xform/([dtnlgkhjqxyvuirzcs])r/$1uan/
    - xform/(\w)t/$1ve/
    - xform/([gkhvuirzcs])y/$1uai/
    - xform/(\w)y/$1ing/
    - xform/([dtnlgkhvuirzcs])o/$1uo/
    - xform/(\w)p/$1un/
    - xform/([jqx])s/$1iong/
    - xform/(\w)s/$1ong/
    - xform/([jqxnl])d/$1iang/
    - xform/(\w)d/$1uang/
    - xform/(\w)f/$1en/
    - xform/(\w)h/$1ang/
    - xform/(\w)j/$1an/
    - xform/(\w)k/$1ao/
    - xform/(\w)l/$1ai/
    - xform/(\w)z/$1ei/
    - xform/(\w)x/$1ie/
    - xform/(\w)c/$1iao/
    - xform/([dtgkhvuirzcs])v/$1ui/
    - xform/(\w)b/$1ou/
    - xform/(\w)m/$1ian/
    - xform/([aoe])\1(\w)/$1$2/
    - "xform/(^|[ '])v/$1zh/"
    - "xform/(^|[ '])i/$1ch/"
    - "xform/(^|[ '])u/$1sh/"
    - xform/([jqxy])v/$1u/
    - xform/([nl])v/$1ü/

reverse_lookup:
  dictionary: stroke
  enable_completion: true
  prefix: "`"
  suffix: "'"
  tips: 〔筆畫〕
  preedit_format:
    - xlit/hspnz/一丨丿丶乙/
  comment_format:
    - xform/([nl])v/$1ü/

punctuator:
  import_preset: default

key_binder:
  import_preset: default

recognizer:
  import_preset: default
  patterns:
    reverse_lookup: "`[a-z]*'?$"
