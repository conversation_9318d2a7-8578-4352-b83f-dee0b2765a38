# Rime schema
# encoding: utf-8

schema:
  schema_id: double_pinyin_flypy
  name: 小鶴雙拼
  version: "0.18"
  author:
    - double pinyin layout by 鶴
    - Rime schema by 佛振 <<EMAIL>>
  description: |
    朙月拼音＋小鶴雙拼方案。
  dependencies:
    - stroke

switches:
  - name: ascii_mode
    reset: 0
    states: [ 中文, 西文 ]
  - name: full_shape
    states: [ 半角, 全角 ]
  - name: simplification
    states: [ 漢字, 汉字 ]
  - name: ascii_punct
    states: [ 。，, ．， ]

engine:
  processors:
    - ascii_composer
    - recognizer
    - key_binder
    - speller
    - punctuator
    - selector
    - navigator
    - express_editor
  segmentors:
    - ascii_segmentor
    - matcher
    - abc_segmentor
    - punct_segmentor
    - fallback_segmentor
  translators:
    - punct_translator
    - reverse_lookup_translator
    - script_translator
  filters:
    - simplifier
    - uniquifier

speller:
  alphabet: zyxwvutsrqponmlkjihgfedcba
  delimiter: " '"
  algebra:
    - erase/^xx$/
    - derive/^([jqxy])u$/$1v/
    - derive/^([aoe])([ioun])$/$1$1$2/
    - xform/^([aoe])(ng)?$/$1$1$2/
    - xform/iu$/Q/
    - xform/(.)ei$/$1W/
    - xform/uan$/R/
    - xform/[uv]e$/T/
    - xform/un$/Y/
    - xform/^sh/U/
    - xform/^ch/I/
    - xform/^zh/V/
    - xform/uo$/O/
    - xform/ie$/P/
    - xform/i?ong$/S/
    - xform/ing$|uai$/K/
    - xform/(.)ai$/$1D/
    - xform/(.)en$/$1F/
    - xform/(.)eng$/$1G/
    - xform/[iu]ang$/L/
    - xform/(.)ang$/$1H/
    - xform/ian$/M/
    - xform/(.)an$/$1J/
    - xform/(.)ou$/$1Z/
    - xform/[iu]a$/X/
    - xform/iao$/N/
    - xform/(.)ao$/$1C/
    - xform/ui$/V/
    - xform/in$/B/
    - xlit/QWRTYUIOPSDFGHJKLZXCVBNM/qwrtyuiopsdfghjklzxcvbnm/
    #- abbrev/^(.).+$/$1/

translator:
  dictionary: luna_pinyin
  prism: double_pinyin_flypy
  preedit_format:
    - xform/([bpmfdtnljqx])n/$1iao/
    - xform/(\w)g/$1eng/
    - xform/(\w)q/$1iu/
    - xform/(\w)w/$1ei/
    - xform/([dtnlgkhjqxyvuirzcs])r/$1uan/
    - xform/(\w)t/$1ve/
    - xform/(\w)y/$1un/
    - xform/([dtnlgkhvuirzcs])o/$1uo/
    - xform/(\w)p/$1ie/
    - xform/([jqx])s/$1iong/
    - xform/(\w)s/$1ong/
    - xform/(\w)d/$1ai/
    - xform/(\w)f/$1en/
    - xform/(\w)h/$1ang/
    - xform/(\w)j/$1an/
    - xform/([gkhvuirzcs])k/$1uai/
    - xform/(\w)k/$1ing/
    - xform/([jqxnl])l/$1iang/
    - xform/(\w)l/$1uang/
    - xform/(\w)z/$1ou/
    - xform/([gkhvuirzcs])x/$1ua/
    - xform/(\w)x/$1ia/
    - xform/(\w)c/$1ao/
    - xform/([dtgkhvuirzcs])v/$1ui/
    - xform/(\w)b/$1in/
    - xform/(\w)m/$1ian/
    - xform/([aoe])\1(\w)/$1$2/
    - "xform/(^|[ '])v/$1zh/"
    - "xform/(^|[ '])i/$1ch/"
    - "xform/(^|[ '])u/$1sh/"
    - xform/([jqxy])v/$1u/
    - xform/([nl])v/$1ü/

reverse_lookup:
  dictionary: stroke
  enable_completion: true
  prefix: "`"
  suffix: "'"
  tips: 〔筆畫〕
  preedit_format:
    - xlit/hspnz/一丨丿丶乙/
  comment_format:
    - xform/([nl])v/$1ü/

punctuator:
  import_preset: default

key_binder:
  import_preset: default

recognizer:
  import_preset: default
  patterns:
    reverse_lookup: "`[a-z]*'?$"
